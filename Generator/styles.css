/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333333;
    background-color: #f8f9fa;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-image {
    height: 60px;
    width: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    filter: brightness(0) invert(1); /* Makes logo white */
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-icon {
    font-size: 2.5rem;
    color: #60a5fa;
}

.logo-section h1 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    color: white; /* White text on purple background */
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-left: 0.5rem;
    color: white; /* White subtitle on purple background */
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-generate {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
    margin-top: 1rem;
}

/* Main Content Layout */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 2rem;
}

/* Sidebar Navigation */
.sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.service-nav h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-nav ul {
    list-style: none;
}

.service-nav li {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
    color: white;
    transform: translateX(4px);
}

.nav-link i {
    width: 16px;
    text-align: center;
}

/* Form Area */
.form-area {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Customer Section */
.customer-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f0f0f0;
}

.customer-section h2 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #5e2e60;
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.form-group input:required:invalid {
    border-color: #e74c3c;
}

/* Services Section */
.services-section {
    margin-top: 2rem;
}

.service-category {
    margin-bottom: 3rem;
    display: none;
}

.service-category.active {
    display: block;
}

.service-category h3 {
    color: #5e2e60;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #9f7ba0;
}

.services-grid {
    display: grid;
    gap: 2rem;
}

.service-subcategory {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #60a5fa;
}

.service-subcategory h4 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.service-item {
    margin-bottom: 1rem;
    position: relative;
}

.service-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.service-label:hover {
    border-color: #60a5fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d0d0d0;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.service-label input[type="checkbox"]:checked + .checkmark {
    background: #5e2e60;
    border-color: #5e2e60;
}

.service-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.service-name {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.service-price {
    font-weight: 600;
    color: #2563eb;
    font-size: 1rem;
}

.service-label input[type="checkbox"]:checked ~ .service-name {
    color: #5e2e60;
}

.service-label input[type="checkbox"]:checked {
    border-color: #5e2e60;
    background: rgba(94, 46, 96, 0.05);
}

/* Custom Input Fields */
.custom-input {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f0f0f0;
    border-radius: 6px;
    display: none;
}

.service-item:has(input[type="checkbox"]:checked) .custom-input {
    display: block;
}

.custom-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: #666;
}

.custom-input input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Calculator Sidebar */
.calculator {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.calculator-content h3 {
    color: #5e2e60;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.selected-services {
    margin-bottom: 1.5rem;
}

.selected-services h4 {
    color: #666;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.services-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-services {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

.selected-service {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85rem;
}

.selected-service:last-child {
    border-bottom: none;
}

.service-name-calc {
    flex: 1;
    color: #333;
}

.service-price-calc {
    color: #2563eb;
    font-weight: 600;
}

.price-summary {
    border-top: 2px solid #f0f0f0;
    padding-top: 1rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.price-row.total {
    font-size: 1.1rem;
    font-weight: 600;
    color: #5e2e60;
    border-top: 1px solid #e0e0e0;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 200px 1fr 280px;
        gap: 1.5rem;
    }
}

@media (max-width: 992px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .sidebar,
    .calculator {
        position: static;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .form-area,
    .sidebar,
    .calculator {
        padding: 1rem;
    }
    
    .logo-section {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .subtitle {
        margin-left: 0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}
